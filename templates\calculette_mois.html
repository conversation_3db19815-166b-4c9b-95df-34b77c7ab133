{% extends 'base.html' %}

{% block head %}
<!-- Custom CSS for Calculette Mois -->
<style>
.calculette-mois-tool {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.monthly-form {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.month-input-group {
    margin-bottom: 1.5rem;
}

.month-input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.month-input-group input, .month-input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.month-input-group input:focus, .month-input-group select:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,0.25);
}

.calculate-month-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin: 1.5rem 0;
}

.calculate-month-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.3);
}

.monthly-result-display {
    background: #e8f5e8;
    border: 2px solid #c3e6c3;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.monthly-result-display h3 {
    margin-bottom: 1rem;
    color: #155724;
    font-size: 1.2rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    margin: 1rem 0;
}

.calendar-day {
    aspect-ratio: 1;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day.work-day {
    background: #d4edda;
    border-color: #c3e6cb;
}

.calendar-day.non-work-day {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.calendar-day:hover {
    transform: scale(1.05);
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block title %}Calculette Mois - Calculatrice Mensuelle d'Heures de Travail | Calculette Mauricette{% endblock %}

{% block description %}Calculette Mois gratuite pour planifier et calculer vos heures de travail mensuelles. Outil de planification mensuelle avec calendrier interactif et calculs automatiques.{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Calculette Mois - Calculatrice Mensuelle",
  "description": "Calculatrice mensuelle d'heures de travail avec planification et calendrier interactif",
  "url": "https://calculette-mauricette.fr/calculette-mois",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "EUR"
  },
  "featureList": [
    "Planification mensuelle",
    "Calendrier interactif",
    "Calcul automatique des heures",
    "Gestion des jours fériés",
    "Rapports mensuels détaillés"
  ],
  "author": {
    "@type": "Organization",
    "name": "Calculette Mauricette",
    "url": "https://calculette-mauricette.fr"
  }
}
</script>
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="bg-light py-3">
  <div class="container">
    <ol class="breadcrumb mb-0">
      <li class="breadcrumb-item"><a href="/" class="text-decoration-none">Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page">Calculette Mois</li>
    </ol>
  </div>
</nav>

<!-- Hero Section -->
<section class="py-5 bg-gradient" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold mb-4">Calculette Mois - Planification Mensuelle d'Heures de Travail</h1>
        <p class="lead mb-4">La <strong>Calculette Mois</strong> est votre outil gratuit de planification mensuelle pour calculer et organiser vos heures de travail sur un mois complet. Planifiez efficacement votre temps de travail avec notre calendrier interactif et nos calculs automatiques.</p>
        <div class="d-flex gap-3 flex-wrap">
          <a href="#calculette-mois" class="btn btn-light btn-lg px-4 py-2">
            <i class="fas fa-calendar-alt me-2"></i>Utiliser la Calculette Mois
          </a>
          <a href="#guide-mensuel" class="btn btn-outline-light btn-lg px-4 py-2">
            <i class="fas fa-question-circle me-2"></i>Guide d'utilisation
          </a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-calendar-alt" style="font-size: 8rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</section>

<!-- Calculette Mois Tool Section -->
<section id="calculette-mois" class="py-5 bg-light">
  <div class="container">
    <div class="calculette-mois-tool">
      <h2 class="text-center mb-4"><i class="fas fa-calendar-alt me-2"></i>Calculette Mois - Planification Mensuelle</h2>

      <div class="monthly-form">
        <!-- Configuration du mois -->
        <div class="row g-3 mb-4">
          <div class="col-md-4">
            <div class="month-input-group">
              <label for="selected-month">Mois :</label>
              <select id="selected-month">
                <option value="0">Janvier</option>
                <option value="1">Février</option>
                <option value="2">Mars</option>
                <option value="3">Avril</option>
                <option value="4">Mai</option>
                <option value="5">Juin</option>
                <option value="6">Juillet</option>
                <option value="7">Août</option>
                <option value="8">Septembre</option>
                <option value="9">Octobre</option>
                <option value="10">Novembre</option>
                <option value="11">Décembre</option>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="month-input-group">
              <label for="selected-year">Année :</label>
              <select id="selected-year">
                <option value="2024">2024</option>
                <option value="2025" selected>2025</option>
                <option value="2026">2026</option>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="month-input-group">
              <label for="daily-hours">Heures par jour :</label>
              <input type="number" id="daily-hours" value="8" min="1" max="12" step="0.5">
            </div>
          </div>
        </div>

        <!-- Paramètres avancés -->
        <div class="row g-3 mb-4">
          <div class="col-md-3">
            <div class="month-input-group">
              <label for="hourly-rate-month">Taux horaire (€) :</label>
              <input type="number" id="hourly-rate-month" step="0.01" min="0" placeholder="Ex: 15.50">
            </div>
          </div>
          <div class="col-md-3">
            <div class="month-input-group">
              <label for="daily-break">Pause quotidienne (min) :</label>
              <input type="number" id="daily-break" value="60" min="0" max="240">
            </div>
          </div>
          <div class="col-md-3">
            <div class="month-input-group">
              <label for="overtime-threshold-month">Seuil heures sup. :</label>
              <input type="number" id="overtime-threshold-month" value="35" min="1" max="50">
            </div>
          </div>
          <div class="col-md-3">
            <div class="month-input-group">
              <label for="overtime-rate-month">Majoration (%) :</label>
              <input type="number" id="overtime-rate-month" value="25" min="0" max="100">
            </div>
          </div>
        </div>

        <!-- Calendrier interactif -->
        <div class="mb-4">
          <h4 class="mb-3"><i class="fas fa-calendar me-2"></i>Calendrier du mois - Cliquez pour marquer les jours travaillés</h4>
          <div class="calendar-grid" id="calendar-grid">
            <!-- Le calendrier sera généré par JavaScript -->
          </div>
          <div class="mt-2">
            <small class="text-muted">
              <span class="badge bg-success me-2">Jour travaillé</span>
              <span class="badge bg-danger me-2">Jour non travaillé</span>
              Cliquez sur les jours pour les marquer comme travaillés ou non travaillés
            </small>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row g-2">
          <div class="col-md-6">
            <button onclick="calculateMonthlyHours()" class="calculate-month-btn">
              <i class="fas fa-calculator me-2"></i>Calculer le Mois
            </button>
          </div>
          <div class="col-md-3">
            <button onclick="markWeekdays()" class="btn btn-outline-success w-100" style="padding: 1rem;">
              <i class="fas fa-business-time me-2"></i>Jours ouvrés
            </button>
          </div>
          <div class="col-md-3">
            <button onclick="resetMonthlyCalculator()" class="btn btn-secondary w-100" style="padding: 1rem;">
              <i class="fas fa-undo me-2"></i>Réinitialiser
            </button>
          </div>
        </div>

        <!-- Résultats mensuels -->
        <div class="monthly-result-display" id="monthly-result" style="display: none;">
          <h3><i class="fas fa-chart-line me-2"></i>Résultats du mois :</h3>
          <div class="row g-3">
            <div class="col-md-6">
              <p id="total-work-days"><strong>Jours travaillés :</strong> --</p>
              <p id="total-monthly-hours"><strong>Total heures :</strong> --</p>
              <p id="average-daily-hours"><strong>Moyenne par jour :</strong> --</p>
            </div>
            <div class="col-md-6">
              <p id="monthly-salary"><strong>Salaire mensuel :</strong> --</p>
              <p id="monthly-overtime"><strong>Heures supplémentaires :</strong> --</p>
              <p id="effective-hourly-rate"><strong>Taux effectif :</strong> --</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Avantages de la Calculette Mois -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Pourquoi utiliser la Calculette Mois ?</h2>
      <p class="lead">Découvrez les avantages uniques de notre outil de planification mensuelle</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 text-center p-4 border-0 shadow-sm">
          <div class="mb-3">
            <i class="fas fa-calendar-check text-success" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Planification Visuelle</h4>
          <p class="text-muted">Visualisez votre mois de travail avec notre calendrier interactif intégré à la calculette mois.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 text-center p-4 border-0 shadow-sm">
          <div class="mb-3">
            <i class="fas fa-chart-pie text-success" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Rapports Détaillés</h4>
          <p class="text-muted">Obtenez des analyses complètes de votre temps de travail mensuel avec graphiques.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 text-center p-4 border-0 shadow-sm">
          <div class="mb-3">
            <i class="fas fa-euro-sign text-success" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Calculs Salariaux</h4>
          <p class="text-muted">Calculez automatiquement votre salaire mensuel avec gestion des heures supplémentaires.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 text-center p-4 border-0 shadow-sm">
          <div class="mb-3">
            <i class="fas fa-mobile-alt text-success" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Interface Responsive</h4>
          <p class="text-muted">Utilisez la calculette mois sur tous vos appareils, ordinateur, tablette ou mobile.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Guide d'utilisation -->
<section id="guide-mensuel" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Comment utiliser la Calculette Mois ?</h2>
      <p class="lead">Guide étape par étape pour optimiser votre planification mensuelle</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">1</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Sélectionner le mois</h4>
          <p class="text-muted">Choisissez le mois et l'année pour lesquels vous souhaitez planifier vos heures de travail avec la calculette mois.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">2</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Configurer les paramètres</h4>
          <p class="text-muted">Définissez vos heures quotidiennes, taux horaire et paramètres de pause pour personnaliser la calculette mois.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">3</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Marquer les jours travaillés</h4>
          <p class="text-muted">Cliquez sur les jours du calendrier pour indiquer vos jours de travail et congés dans la calculette mois.</p>
        </div>
      </div>
    </div>

    <div class="row g-4 mt-2">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">4</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Calculer automatiquement</h4>
          <p class="text-muted">Lancez le calcul pour obtenir vos totaux mensuels, salaires et statistiques détaillées.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">5</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Analyser les résultats</h4>
          <p class="text-muted">Consultez vos rapports mensuels avec graphiques et tableaux de répartition des heures.</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 p-4 border-0 shadow-sm">
          <div class="text-center mb-3">
            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">6</span>
            </div>
          </div>
          <h4 class="h5 mb-3 text-center">Exporter et partager</h4>
          <p class="text-muted">Exportez vos résultats ou partagez votre planification mensuelle avec votre équipe.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Applications professionnelles -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Applications Professionnelles de la Calculette Mois</h2>
      <p class="lead">Découvrez comment la calculette mois s'adapte à différents secteurs d'activité</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-building text-success" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Entreprises et PME</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Planification des équipes sur le mois</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gestion des congés et absences</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Calcul des masses salariales mensuelles</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Optimisation des ressources humaines</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-user-tie text-success" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Consultants et Freelances</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Planification des missions mensuelles</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Facturation précise des prestations</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gestion de plusieurs clients</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Optimisation du temps facturable</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-users text-success" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Gestionnaires RH</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Suivi des heures des employés</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Calcul des heures supplémentaires</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Planification des rotations</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Reporting mensuel automatisé</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Tableau comparatif -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Calculette Mois vs Planification Traditionnelle</h2>
      <p class="lead">Découvrez les avantages de notre outil de planification mensuelle</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="table-responsive bg-white rounded shadow-sm">
          <table class="table table-striped table-hover mb-0">
            <thead class="table-success">
              <tr>
                <th scope="col">Critère</th>
                <th scope="col" class="text-center">Calculette Mois</th>
                <th scope="col" class="text-center">Méthodes traditionnelles</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>Facilité d'utilisation</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Interface intuitive</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Complexe et chronophage</td>
              </tr>
              <tr>
                <td><strong>Calculs automatiques</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Instantanés et précis</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Manuels et sujets aux erreurs</td>
              </tr>
              <tr>
                <td><strong>Visualisation</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Calendrier interactif</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Tableaux statiques</td>
              </tr>
              <tr>
                <td><strong>Gestion des heures sup.</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Automatique</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Calcul manuel</td>
              </tr>
              <tr>
                <td><strong>Accessibilité</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> 24h/24 en ligne</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Limitée aux heures de bureau</td>
              </tr>
              <tr>
                <td><strong>Coût</strong></td>
                <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Gratuit</td>
                <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Logiciels payants</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions Fréquentes - Calculette Mois</h2>
      <p class="lead">Trouvez les réponses aux questions les plus courantes sur notre outil de planification mensuelle</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordionMois">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingMoisOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMoisOne" aria-expanded="true" aria-controls="collapseMoisOne">
                Comment fonctionne la Calculette Mois exactement ?
              </button>
            </h3>
            <div id="collapseMoisOne" class="accordion-collapse collapse show" aria-labelledby="headingMoisOne" data-bs-parent="#faqAccordionMois">
              <div class="accordion-body">
                <p>La <strong>Calculette Mois</strong> vous permet de planifier et calculer vos heures de travail sur un mois complet. Vous sélectionnez le mois, configurez vos paramètres de travail, puis marquez les jours travaillés sur le calendrier interactif.</p>
                <p>L'outil calcule automatiquement vos totaux mensuels, heures supplémentaires, et salaires en tenant compte de vos pauses et jours de congé.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingMoisTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMoisTwo" aria-expanded="false" aria-controls="collapseMoisTwo">
                Puis-je utiliser la Calculette Mois pour plusieurs employés ?
              </button>
            </h3>
            <div id="collapseMoisTwo" class="accordion-collapse collapse" aria-labelledby="headingMoisTwo" data-bs-parent="#faqAccordionMois">
              <div class="accordion-body">
                <p>Oui, la <strong>calculette mois</strong> peut être utilisée pour planifier les heures de plusieurs employés. Vous pouvez effectuer des calculs séparés pour chaque employé et exporter les résultats individuellement.</p>
                <p>Pour une gestion d'équipe plus avancée, nous recommandons d'utiliser notre outil en combinaison avec des feuilles de calcul pour consolider les données.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingMoisThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMoisThree" aria-expanded="false" aria-controls="collapseMoisThree">
                Comment gérer les jours fériés avec la Calculette Mois ?
              </button>
            </h3>
            <div id="collapseMoisThree" class="accordion-collapse collapse" aria-labelledby="headingMoisThree" data-bs-parent="#faqAccordionMois">
              <div class="accordion-body">
                <p>Les jours fériés sont gérés simplement dans la <strong>calculette mois</strong> : il suffit de ne pas les marquer comme jours travaillés sur le calendrier interactif.</p>
                <p>Vous pouvez également utiliser le bouton "Jours ouvrés" pour marquer automatiquement les jours de semaine, puis décocher manuellement les jours fériés selon votre calendrier.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingMoisFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMoisFour" aria-expanded="false" aria-controls="collapseMoisFour">
                La Calculette Mois calcule-t-elle automatiquement les heures supplémentaires ?
              </button>
            </h3>
            <div id="collapseMoisFour" class="accordion-collapse collapse" aria-labelledby="headingMoisFour" data-bs-parent="#faqAccordionMois">
              <div class="accordion-body">
                <p>Oui, la <strong>calculette mois</strong> calcule automatiquement les heures supplémentaires en fonction du seuil que vous définissez (par défaut 35h par semaine).</p>
                <p>Vous pouvez personnaliser le seuil d'heures supplémentaires et le taux de majoration selon votre convention collective ou contrat de travail.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingMoisFive">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMoisFive" aria-expanded="false" aria-controls="collapseMoisFive">
                Puis-je exporter les résultats de la Calculette Mois ?
              </button>
            </h3>
            <div id="collapseMoisFive" class="accordion-collapse collapse" aria-labelledby="headingMoisFive" data-bs-parent="#faqAccordionMois">
              <div class="accordion-body">
                <p>Absolument ! La <strong>calculette mois</strong> permet d'exporter vos résultats sous différents formats pour faciliter votre reporting et archivage.</p>
                <p>Vous pouvez également partager vos planifications mensuelles avec votre équipe ou votre gestionnaire RH directement depuis l'interface.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Liens vers autres outils -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Outils Complémentaires - Suite Calculette Mauricette</h2>
      <p class="lead">Découvrez nos autres outils de calcul d'heures pour tous vos besoins</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-clock text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mauricette</h3>
            <p class="card-text">Calcul d'heures quotidien avec gestion des pauses</p>
            <div class="mt-auto pt-3">
              <a href="/" class="btn btn-outline-primary d-block">Utiliser</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-business-time text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Heure</h3>
            <p class="card-text">Calcul d'heures avancé avec fonctionnalités étendues</p>
            <div class="mt-auto pt-3">
              <a href="/calculette-heure" class="btn btn-outline-primary d-block">Utiliser</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-calendar-week text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Semaine</h3>
            <p class="card-text">Planification et calcul hebdomadaire des heures</p>
            <div class="mt-auto pt-3">
              <a href="/calculette-semaine" class="btn btn-outline-primary d-block">Utiliser</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm border-0 bg-success text-white">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-calendar-alt text-white" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mois</h3>
            <p class="card-text">Outil actuel - Planification mensuelle complète</p>
            <div class="mt-auto pt-3">
              <span class="btn btn-light d-block">Page actuelle</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-success text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Prêt à planifier votre mois avec la Calculette Mois ?</h2>
        <p class="lead mb-4">Utilisez notre outil gratuit de planification mensuelle pour optimiser votre temps de travail et calculer vos heures avec précision !</p>
        <a href="#calculette-mois" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calendar-alt me-2"></i>Commencer la planification mensuelle
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales pour la calculette mois
let currentMonth = new Date().getMonth();
let currentYear = new Date().getFullYear();
let workDays = new Set();

// Initialisation de la calculette mois
document.addEventListener('DOMContentLoaded', function() {
  // Définir le mois actuel par défaut
  document.getElementById('selected-month').value = currentMonth;
  document.getElementById('selected-year').value = currentYear;

  // Générer le calendrier initial
  generateCalendar();

  // Écouter les changements de mois/année
  document.getElementById('selected-month').addEventListener('change', function() {
    currentMonth = parseInt(this.value);
    generateCalendar();
  });

  document.getElementById('selected-year').addEventListener('change', function() {
    currentYear = parseInt(this.value);
    generateCalendar();
  });
});

// Génération du calendrier interactif
function generateCalendar() {
  const calendarGrid = document.getElementById('calendar-grid');
  calendarGrid.innerHTML = '';

  // En-têtes des jours de la semaine
  const dayHeaders = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
  dayHeaders.forEach(day => {
    const headerDiv = document.createElement('div');
    headerDiv.textContent = day;
    headerDiv.style.fontWeight = 'bold';
    headerDiv.style.textAlign = 'center';
    headerDiv.style.padding = '0.5rem';
    headerDiv.style.backgroundColor = '#f8f9fa';
    calendarGrid.appendChild(headerDiv);
  });

  // Calculer le premier jour du mois et le nombre de jours
  const firstDay = new Date(currentYear, currentMonth, 1);
  const lastDay = new Date(currentYear, currentMonth + 1, 0);
  const daysInMonth = lastDay.getDate();

  // Ajuster pour que lundi soit le premier jour (0)
  let startDay = firstDay.getDay() - 1;
  if (startDay < 0) startDay = 6;

  // Ajouter des cellules vides pour les jours précédents
  for (let i = 0; i < startDay; i++) {
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'calendar-day';
    emptyDiv.style.backgroundColor = '#f8f9fa';
    calendarGrid.appendChild(emptyDiv);
  }

  // Ajouter les jours du mois
  for (let day = 1; day <= daysInMonth; day++) {
    const dayDiv = document.createElement('div');
    dayDiv.className = 'calendar-day';
    dayDiv.textContent = day;
    dayDiv.dataset.day = day;

    // Vérifier si c'est un jour travaillé
    const dayKey = `${currentYear}-${currentMonth}-${day}`;
    if (workDays.has(dayKey)) {
      dayDiv.classList.add('work-day');
    } else {
      dayDiv.classList.add('non-work-day');
    }

    // Ajouter l'événement de clic
    dayDiv.addEventListener('click', function() {
      toggleWorkDay(day);
    });

    calendarGrid.appendChild(dayDiv);
  }
}

// Basculer un jour entre travaillé/non travaillé
function toggleWorkDay(day) {
  const dayKey = `${currentYear}-${currentMonth}-${day}`;
  const dayElement = document.querySelector(`[data-day="${day}"]`);

  if (workDays.has(dayKey)) {
    workDays.delete(dayKey);
    dayElement.classList.remove('work-day');
    dayElement.classList.add('non-work-day');
  } else {
    workDays.add(dayKey);
    dayElement.classList.remove('non-work-day');
    dayElement.classList.add('work-day');
  }
}

// Marquer automatiquement les jours ouvrés (lundi à vendredi)
function markWeekdays() {
  workDays.clear();

  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(currentYear, currentMonth, day);
    const dayOfWeek = date.getDay();

    // Si c'est un jour de semaine (lundi=1 à vendredi=5)
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      const dayKey = `${currentYear}-${currentMonth}-${day}`;
      workDays.add(dayKey);
    }
  }

  generateCalendar();
}

// Calcul des heures mensuelles
function calculateMonthlyHours() {
  const dailyHours = parseFloat(document.getElementById('daily-hours').value) || 8;
  const hourlyRate = parseFloat(document.getElementById('hourly-rate-month').value) || 0;
  const dailyBreak = parseInt(document.getElementById('daily-break').value) || 60;
  const overtimeThreshold = parseFloat(document.getElementById('overtime-threshold-month').value) || 35;
  const overtimeRate = parseFloat(document.getElementById('overtime-rate-month').value) || 25;

  // Compter les jours travaillés ce mois
  const workDaysThisMonth = Array.from(workDays).filter(dayKey => {
    const [year, month] = dayKey.split('-').map(Number);
    return year === currentYear && month === currentMonth;
  }).length;

  if (workDaysThisMonth === 0) {
    alert('Veuillez marquer au moins un jour travaillé sur le calendrier');
    return;
  }

  // Calculs de base
  const effectiveDailyHours = dailyHours - (dailyBreak / 60);
  const totalMonthlyHours = workDaysThisMonth * effectiveDailyHours;
  const averageDailyHours = totalMonthlyHours / workDaysThisMonth;

  // Calcul des heures supplémentaires (basé sur 4.33 semaines par mois)
  const weeklyHours = totalMonthlyHours / 4.33;
  const overtimeHours = Math.max(0, weeklyHours - overtimeThreshold) * 4.33;
  const regularHours = totalMonthlyHours - overtimeHours;

  // Calculs salariaux
  let monthlySalary = 0;
  let effectiveHourlyRate = 0;

  if (hourlyRate > 0) {
    const overtimeHourlyRate = hourlyRate * (1 + overtimeRate / 100);
    monthlySalary = (regularHours * hourlyRate) + (overtimeHours * overtimeHourlyRate);
    effectiveHourlyRate = monthlySalary / totalMonthlyHours;
  }

  // Affichage des résultats
  document.getElementById('total-work-days').innerHTML = `<strong>Jours travaillés :</strong> ${workDaysThisMonth} jours`;
  document.getElementById('total-monthly-hours').innerHTML = `<strong>Total heures :</strong> ${totalMonthlyHours.toFixed(1)}h`;
  document.getElementById('average-daily-hours').innerHTML = `<strong>Moyenne par jour :</strong> ${averageDailyHours.toFixed(1)}h`;

  if (hourlyRate > 0) {
    document.getElementById('monthly-salary').innerHTML = `<strong>Salaire mensuel :</strong> ${monthlySalary.toFixed(2)}€`;
    document.getElementById('effective-hourly-rate').innerHTML = `<strong>Taux effectif :</strong> ${effectiveHourlyRate.toFixed(2)}€/h`;
  } else {
    document.getElementById('monthly-salary').innerHTML = `<strong>Salaire mensuel :</strong> --`;
    document.getElementById('effective-hourly-rate').innerHTML = `<strong>Taux effectif :</strong> --`;
  }

  document.getElementById('monthly-overtime').innerHTML = `<strong>Heures supplémentaires :</strong> ${overtimeHours.toFixed(1)}h`;

  // Afficher les résultats
  document.getElementById('monthly-result').style.display = 'block';

  // Stocker les résultats pour l'export
  window.monthlyResults = {
    month: currentMonth,
    year: currentYear,
    workDays: workDaysThisMonth,
    totalHours: totalMonthlyHours,
    overtimeHours: overtimeHours,
    monthlySalary: monthlySalary,
    dailyHours: dailyHours,
    hourlyRate: hourlyRate
  };
}

// Réinitialiser la calculette
function resetMonthlyCalculator() {
  workDays.clear();
  document.getElementById('daily-hours').value = '8';
  document.getElementById('hourly-rate-month').value = '';
  document.getElementById('daily-break').value = '60';
  document.getElementById('overtime-threshold-month').value = '35';
  document.getElementById('overtime-rate-month').value = '25';

  document.getElementById('monthly-result').style.display = 'none';

  generateCalendar();
}

// Fonction d'export des résultats mensuels
function exportMonthlyResults() {
  if (!window.monthlyResults) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const results = window.monthlyResults;
  const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                     'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];

  const exportData = `Calculette Mois - Résultats ${monthNames[results.month]} ${results.year}
=====================================
Période: ${monthNames[results.month]} ${results.year}
Jours travaillés: ${results.workDays}
Heures quotidiennes: ${results.dailyHours}h
Total heures mensuelles: ${results.totalHours.toFixed(1)}h
Heures supplémentaires: ${results.overtimeHours.toFixed(1)}h
${results.hourlyRate > 0 ? `Salaire mensuel: ${results.monthlySalary.toFixed(2)}€` : ''}

Généré le ${new Date().toLocaleString('fr-FR')}
`;

  const blob = new Blob([exportData], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `calculette-mois-${monthNames[results.month].toLowerCase()}-${results.year}.txt`;
  a.click();
  URL.revokeObjectURL(url);
}

// Smooth scrolling pour les liens d'ancrage
document.addEventListener('DOMContentLoaded', function() {
  const anchorLinks = document.querySelectorAll('a[href^="#"]');

  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
</script>
{% endblock %}
